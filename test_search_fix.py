#!/usr/bin/env python3
"""
測試搜尋功能修復的腳本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.taiex import search_first_url as taiex_search
from utils.us import search_first_url as us_search

def test_taiex_search():
    """測試台灣股市搜尋功能"""
    print("=== 測試台灣股市搜尋功能 ===")
    
    test_query = "台積電 股價"
    print(f"搜尋關鍵字: {test_query}")
    
    try:
        results = taiex_search(test_query, wanted_source="經濟日報")
        if results:
            print(f"搜尋成功，找到 {len(results)} 則結果")
            for i, result in enumerate(results[:3]):  # 只顯示前3則
                print(f"  {i+1}. 標題: {result.get('title', 'N/A')}")
                print(f"     來源: {result.get('source', 'N/A')}")
                print(f"     URL: {result.get('url', 'N/A')}")
                print()
        else:
            print("搜尋失敗，沒有找到結果")
    except Exception as e:
        print(f"搜尋過程中發生錯誤: {e}")

def test_us_search():
    """測試美股搜尋功能"""
    print("=== 測試美股搜尋功能 ===")
    
    test_query = "蘋果 Apple 股價"
    print(f"搜尋關鍵字: {test_query}")
    
    try:
        results = us_search(test_query, strict_mode=False)
        if results:
            print(f"搜尋成功，找到 {len(results)} 則結果")
            for i, result in enumerate(results[:3]):  # 只顯示前3則
                print(f"  {i+1}. 標題: {result.get('title', 'N/A')}")
                print(f"     來源: {result.get('source', 'N/A')}")
                print(f"     URL: {result.get('url', 'N/A')}")
                print()
        else:
            print("搜尋失敗，沒有找到結果")
    except Exception as e:
        print(f"搜尋過程中發生錯誤: {e}")

def test_taiex4_function():
    """測試 taiex4 函數的返回值"""
    print("=== 測試 taiex4 函數返回值 ===")
    
    from utils.taiex import taiex4
    
    # 模擬一些測試數據
    test_lines = [
        "2025-01-31",
        "新聞摘要",
        "台積電股價上漲 (經濟)",
        "聯發科業績亮眼 (工商)",
        "加權指數收盤"
    ]
    
    try:
        # 這裡需要一個有效的 API key，但我們只測試返回值結構
        result = taiex4(test_lines, "test_api_key")
        
        if isinstance(result, tuple) and len(result) == 2:
            to_crawl_taiex5, taiex4_result = result
            print("✓ taiex4 函數返回值結構正確")
            print(f"  to_crawl_taiex5 類型: {type(to_crawl_taiex5)}")
            print(f"  taiex4_result 類型: {type(taiex4_result)}")
            
            if isinstance(to_crawl_taiex5, list):
                print(f"  to_crawl_taiex5 長度: {len(to_crawl_taiex5)}")
                if to_crawl_taiex5:
                    print(f"  第一個元素結構: {list(to_crawl_taiex5[0].keys()) if isinstance(to_crawl_taiex5[0], dict) else 'Not a dict'}")
        else:
            print("✗ taiex4 函數返回值結構不正確")
            print(f"  實際返回: {type(result)}, 長度: {len(result) if hasattr(result, '__len__') else 'N/A'}")
            
    except Exception as e:
        print(f"測試 taiex4 函數時發生錯誤: {e}")

if __name__ == "__main__":
    print("開始測試搜尋功能修復...")
    print()
    
    test_taiex_search()
    print()
    
    test_us_search()
    print()
    
    test_taiex4_function()
    print()
    
    print("測試完成！")
